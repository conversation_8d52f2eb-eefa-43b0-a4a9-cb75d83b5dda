@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Markdown content wrapping fixes */
.markdown-content {
  word-wrap: break-word !important;
  overflow-wrap: anywhere !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.markdown-content * {
  word-wrap: break-word !important;
  overflow-wrap: anywhere !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem !important;
  margin-left: 0 !important;
}

.markdown-content li {
  white-space: normal !important;
  word-break: break-word !important;
}

.markdown-content strong,
.markdown-content b,
.markdown-content em,
.markdown-content i {
  white-space: normal !important;
  word-break: break-word !important;
}

.markdown-content code {
  white-space: pre-wrap !important;
  word-break: break-all !important;
}

.markdown-content pre:not(.syntax-highlighter-pre) {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: anywhere !important;
  overflow: hidden !important;
}

/* Don't apply markdown styles to syntax highlighter components */
.syntax-highlighter-container,
.syntax-highlighter-container * {
  word-wrap: initial !important;
  overflow-wrap: initial !important;
}

/* Prism.js theme classes for optimized theme switching */
.prism-theme-light {
  --prism-background: #fafafa;
  --prism-foreground: #383a42;
  --prism-comment: #a0a1a7;
  --prism-keyword: #a626a4;
  --prism-string: #50a14f;
  --prism-number: #986801;
  --prism-function: #4078f2;
  --prism-operator: #383a42;
  --prism-punctuation: #383a42;
  --prism-property: #e45649;
  --prism-tag: #e45649;
  --prism-attr-name: #986801;
  --prism-attr-value: #50a14f;
  --prism-boolean: #986801;
  --prism-constant: #986801;
  --prism-deleted: #e45649;
  --prism-inserted: #50a14f;
  --prism-variable: #e45649;
  --prism-class-name: #c18401;
}

.prism-theme-dark {
  --prism-background: #282c34;
  --prism-foreground: #abb2bf;
  --prism-comment: #5c6370;
  --prism-keyword: #c678dd;
  --prism-string: #98c379;
  --prism-number: #d19a66;
  --prism-function: #61afef;
  --prism-operator: #abb2bf;
  --prism-punctuation: #abb2bf;
  --prism-property: #e06c75;
  --prism-tag: #e06c75;
  --prism-attr-name: #d19a66;
  --prism-attr-value: #98c379;
  --prism-boolean: #d19a66;
  --prism-constant: #d19a66;
  --prism-deleted: #e06c75;
  --prism-inserted: #98c379;
  --prism-variable: #e06c75;
  --prism-class-name: #e5c07b;
}

/* Apply theme colors to syntax elements */
.syntax-highlighter-container pre[class*="language-"] {
  background: var(--prism-background) !important;
  color: var(--prism-foreground) !important;
}

.syntax-highlighter-container .token.comment,
.syntax-highlighter-container .token.prolog,
.syntax-highlighter-container .token.doctype,
.syntax-highlighter-container .token.cdata {
  color: var(--prism-comment) !important;
}

.syntax-highlighter-container .token.keyword,
.syntax-highlighter-container .token.selector,
.syntax-highlighter-container .token.important,
.syntax-highlighter-container .token.atrule {
  color: var(--prism-keyword) !important;
}

.syntax-highlighter-container .token.string,
.syntax-highlighter-container .token.char,
.syntax-highlighter-container .token.attr-value,
.syntax-highlighter-container .token.regex,
.syntax-highlighter-container .token.variable {
  color: var(--prism-string) !important;
}

.syntax-highlighter-container .token.number,
.syntax-highlighter-container .token.boolean,
.syntax-highlighter-container .token.constant {
  color: var(--prism-number) !important;
}

.syntax-highlighter-container .token.function,
.syntax-highlighter-container .token.method {
  color: var(--prism-function) !important;
}

.syntax-highlighter-container .token.operator,
.syntax-highlighter-container .token.entity,
.syntax-highlighter-container .token.url {
  color: var(--prism-operator) !important;
}

.syntax-highlighter-container .token.punctuation {
  color: var(--prism-punctuation) !important;
}

.syntax-highlighter-container .token.property,
.syntax-highlighter-container .token.tag {
  color: var(--prism-property) !important;
}

.syntax-highlighter-container .token.attr-name {
  color: var(--prism-attr-name) !important;
}

.syntax-highlighter-container .token.class-name {
  color: var(--prism-class-name) !important;
}

.syntax-highlighter-container .token.deleted {
  color: var(--prism-deleted) !important;
}

.syntax-highlighter-container .token.inserted {
  color: var(--prism-inserted) !important;
}